services:
  sx-jiliang-chat-v2:
    image: rag_chat:v1.1.15
    container_name: sx-jiliang-chat-v2
    
    # 内存配置 - 平衡多智能体需求与集群环境
    shm_size: '4gb'                 # LLM推理需要的共享内存
    mem_limit: 18g                  # 足够避免OOM，平衡集群资源使用
    mem_reservation: 8g             # 预留基础内存确保稳定运行
    mem_swappiness: 5               # 最小化swap使用，保证性能
    oom_score_adj: -500             # 降低被OOM Killer杀死的概率

    # CPU配置 - 充分利用集群空闲CPU资源
    cpus: '5.0'                     # 利用集群CPU空闲资源（8-16%使用率）
    cpu_shares: 1024                # 高优先级CPU调度

    # 健康检查和重启策略
    restart: unless-stopped         # 避免因OOM频繁重启
    working_dir: /project
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:18800/jiliang/stats/functions"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s             # 给应用足够启动时间

    # 系统限制优化
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
      nproc: 32768                  # 支持多worker进程
      memlock:                      # 允许内存锁定，提升性能
        soft: -1
        hard: -1

    # 端口映射
    ports:
      - "18802:18800"
      #- "18805:18800"

    # 卷挂载
    volumes:
      - ./:/project
      - /dev/shm:/dev/shm           # 挂载共享内存，提升I/O性能

    # 环境变量 - 多智能体系统优化
    environment:
      - PYTHONPATH=/project:/project/rag_chat
    command: python rag_chat/jiliang_chat.py

  sx-wps-chat-v2:
    image: python-v2:1.0
    container_name: sx-wps-chat-v2
    shm_size: '4gb'
    mem_limit: 2g
    restart: always
    working_dir: /project
    ports:
      - "18801:18801"
      #- "18804:18801"
    volumes:
      - ./:/project
    command: python rag_chat/wps_chat.py